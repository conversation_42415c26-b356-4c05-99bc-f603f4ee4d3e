import React, { useEffect, useCallback, useState } from "react";
import { toast } from "react-toastify";
import { useDispatch, useSelector } from "react-redux";
import {
	closeMediaLibrary,
	resetMedia,
	toggleFolderSelection,
	toggleImageSelection,
	setFolders,
	setMedia,
	appendMedia,
	setMediaPagination,
	navigateToFolder,
	setSelectedFolders,
} from "../../store/slices/mediaLibrarySlice";

import {
	useGetFolderListMutation,
	useGetMediaLibraryListMutation,
	useCreateFolderMutation,
	useDeleteFolderMutation,
	useDeleteMediaMutation,
	useUpdateMediaMutation,
} from "../../store/apis/mediaLibraryApi";
import { FiX } from "react-icons/fi";
import OutsideClickHandler from "react-outside-click-handler";
import Aside from "./components/Aside";

import styles from "./MediaLibrary.module.css";
import Filter from "./components/Filter";
import { PiFolderSimplePlusThin } from "react-icons/pi";
import { MdFolder } from "react-icons/md";

import KebabMenu from "./components/KebabMenu";
import { AnimatedButtonLoader } from "../../parts/Loader";
import FileInfo from "./components/FileInfo";
import { Input } from "../../parts/FormComponents";
import Button from "../../parts/Button";
import ConfirmationModal from "../../parts/ConfirmationModal";
import useConfirmationModal from "../../utils/useConfirmationModal";
import {
	runMediaLibraryCallback,
	clearMediaLibraryCallback,
} from "../../utils/MediaLibraryManager";
// import EditMediaModal from "./components/EditMediaModal";
const MediaLibrary = () => {
	const dispatch = useDispatch();
	const [showCreateFolder, setShowCreateFolder] = useState(false);
	const [newFolderName, setNewFolderName] = useState("");
	const [folderToDelete, setFolderToDelete] = useState(null);
	const [mediaToDelete, setMediaToDelete] = useState(null);
	// const [showEditMedia, setShowEditMedia] = useState(false);
	// const [mediaToEdit, setMediaToEdit] = useState(null);
	const [updatedMediaCache, setUpdatedMediaCache] = useState({});
	// Use local state to track current offset
	const [currentOffset, setCurrentOffset] = useState(0);
	// Ref for scroll container
	const scrollContainerRef = React.useRef(null);

	// Confirmation modal hook
	const { isModalOpen, openModal, closeModal } = useConfirmationModal();
	const {
		filters,
		isMultiple,
		fileType,
		folderPath,
		selectedFolders,
		selectedImages,
		currentFolder,
		folderHierarchy,
		folders,
		media,
		mediaPagination,
	} = useSelector((state) => state.mediaLibrary);

	const [getFolderList, { isLoading: isLoadingFolders }] = useGetFolderListMutation();
	const [getMediaLibraryList, { isLoading: isLoadingMedia, reset }] =
		useGetMediaLibraryListMutation();
	const [createFolder, { isLoading: isCreatingFolder }] = useCreateFolderMutation();
	const [deleteFolder, { isLoading: isDeletingFolder }] = useDeleteFolderMutation();
	const [deleteMedia, { isLoading: isDeletingMedia }] = useDeleteMediaMutation();

	const loadFolders = useCallback(async () => {
		try {
			const response = await getFolderList({
				search: filters.search || "",
				parent: currentFolder || "",
				level: 0,
				limit: 1000, // Load all folders at once as per requirement
				offset: 0,
			}).unwrap();

			if (response.status === "success") {
				dispatch(setFolders(response.data.data || []));
			}
		} catch (error) {
			console.error("Error loading folders:", error);
			toast.error("Failed to load folders");
		}
	}, [dispatch, getFolderList, filters.search, currentFolder]);

	const loadMediaList = useCallback(
		async (isNew = false) => {
			try {
				// Reset offset if loading new data
				if (isNew) {
					setCurrentOffset(0);
				}
				
				// Use local offset state
				const offsetToUse = isNew ? 0 : currentOffset;
				console.log("loadMediaList using offset:", offsetToUse);

				const response = await getMediaLibraryList({
					search: filters.search || "",
					folder: currentFolder || "",
					includeSubfolders: false,
					limit: mediaPagination.limit,
					offset: offsetToUse,
				}).unwrap();

				if (response.status === "success") {
					const newMedia = response.data || [];
					if (isNew) {
						dispatch(setMedia(newMedia));
						// Update local offset for next page
						setCurrentOffset(mediaPagination.limit);
					} else {
						// When loading more, append to existing media
						dispatch(appendMedia(newMedia));
						// Update local offset for next page
						setCurrentOffset(offsetToUse + mediaPagination.limit);
					}

					dispatch(
						setMediaPagination({
							hasMore: newMedia.length === mediaPagination.limit,
							totalCount: response.totalCounts || 0,
						})
					);
				}
			} catch (error) {
				console.error("Error loading media:", error);
				toast.error("Failed to load media");
				dispatch(setMediaPagination({ hasMore: false }));
			}
		},
		[
			dispatch,
			getMediaLibraryList,
			filters.search,
			currentFolder,
			mediaPagination.offset,
			mediaPagination.limit,
		]
	);

	const handleRefreshMediaLibrary = async () => {
		try {
			// Reset local offset when refreshing
			setCurrentOffset(0);
			
			const response = await getMediaLibraryList({
				search: filters.search || "",
				folder: currentFolder || "",
				includeSubfolders: false,
				limit: mediaPagination.limit,
				offset: 0, // Always start from 0 when refreshing
			}).unwrap();
			
			// Update the media data
			dispatch(setMedia(response.data || []));
			
			// Update local offset for next page
			setCurrentOffset(mediaPagination.limit);
			
			// Update pagination state
			dispatch(setMediaPagination({
				hasMore: (response.data || []).length === mediaPagination.limit,
				totalCount: response.totalCounts || 0
			}));
		} catch (error) {
			console.error("Failed to refresh media library:", error);
		}
	};
	const loadFoldersAndMedia = useCallback(async () => {
		await Promise.all([loadFolders(), loadMediaList(true)]);
	}, [loadFolders, loadMediaList]);
	useEffect(() => {
		loadFoldersAndMedia();
	}, [loadFoldersAndMedia]);

	const handleAddFolderClick = () => {
		setShowCreateFolder(true);
	};

	const handleCreateFolder = async () => {
		if (!newFolderName.trim()) {
			toast.error("Please enter a folder name");
			return;
		}

		try {
			// Determine the current level and parent based on folder hierarchy
			const level = folderHierarchy.length;
			const parent = currentFolder || null;

			// Create the path based on hierarchy
			let path = "/" + newFolderName.trim().toLowerCase().replace(/\s+/g, "");
			if (folderHierarchy.length > 0) {
				// If we're inside folders, the path should be relative to current location
				const currentPath = folderHierarchy
					.map((f) => f.name.toLowerCase().replace(/\s+/g, ""))
					.join("/");
				path = "/" + currentPath + "/" + newFolderName.trim().toLowerCase().replace(/\s+/g, "");
			}

			const folderData = {
				name: newFolderName.trim(),
				path: path,
				level: level,
				parent: parent,
			};

			await createFolder(folderData).unwrap();

			// Reset form and close modal
			setNewFolderName("");
			setShowCreateFolder(false);

			// Reload folders to show the new folder
			await loadFolders();

			toast.success("Folder created successfully");
		} catch (error) {
			console.error("Failed to create folder:", error);
			toast.error("Failed to create folder");
		}
	};

	const handleCancelCreateFolder = () => {
		setNewFolderName("");
		setShowCreateFolder(false);
	};

	const handleCloseDeleteModal = () => {
		setFolderToDelete(null);
		setMediaToDelete(null);
		closeModal();
	};
	const handleShowFolderDetails = (folder) => {
		//Code_Change on click of the folder this function gets called which shows the folder details
		// and i can see the selectd folder name in the right side
		dispatch(toggleFolderSelection(folder));
	};
	const handleFolderEnter = (folder) => {
		// Reset offset when navigating to a new folder
		setCurrentOffset(0);
		// Navigate into the folder
		dispatch(navigateToFolder({ folder }));
	};

	const handleBreadcrumbClick = (index) => {
		// Reset offset when navigating via breadcrumbs
		setCurrentOffset(0);
		
		if (index === -1) {
			// Navigate to root
			dispatch(navigateToFolder({ isRoot: true }));
		} else {
			// Navigate to specific folder in hierarchy
			const folder = folderHierarchy[index];
			dispatch(navigateToFolder({ folder }));
		}
	};

	const handleLoadMore = async () => {
		if (mediaPagination.hasMore && !isLoadingMedia) {
			// Save current scroll position
			const scrollContainer = scrollContainerRef.current;
			const scrollPosition = scrollContainer ? scrollContainer.scrollTop : 0;
			const scrollHeight = scrollContainer ? scrollContainer.scrollHeight : 0;
			
			console.log("Saved scroll position:", scrollPosition, "Scroll height:", scrollHeight);
			
			try {
				// Use local state offset
				const requestPayload = {
					search: filters.search || "",
					folder: currentFolder || "",
					includeSubfolders: false,
					limit: mediaPagination.limit,
					offset: currentOffset,
				};
				
				// Directly fetch more media
				const response = await getMediaLibraryList(requestPayload).unwrap();

				if (response.status === "success") {
					const newMedia = response.data || [];
					
					// Append the new media to existing media
					dispatch(appendMedia(newMedia));
					
					// Update local offset
					const nextOffset = currentOffset + mediaPagination.limit;
					setCurrentOffset(nextOffset);
					
					// Update pagination state
					dispatch(
						setMediaPagination({
							hasMore: newMedia.length === mediaPagination.limit,
							totalCount: response.totalCounts || 0,
						})
					);
					
					// Restore scroll position after render
					setTimeout(() => {
						if (scrollContainer) {
							// Calculate how much content was added
							const newScrollHeight = scrollContainer.scrollHeight;
							const addedHeight = newScrollHeight - scrollHeight;
							
							// Set scroll position to previous position
							scrollContainer.scrollTop = scrollPosition;
							console.log("Restored scroll to:", scrollPosition);
						}
					}, 100);
				}
			} catch (error) {
				console.error("Error loading more media:", error);
				toast.error("Failed to load more media");
			}
		}
	};

	const getBreadcrumbPath = () => {
		let path = "Root";
		if (folderHierarchy.length > 0) {
			path += " > " + folderHierarchy.map((f) => f.name).join(" > ");
		}
		return path;
	};

	// Helper function to check if a folder is selected
	const isFolderSelected = (folderId) => {
		return selectedFolders.some((folder) => (folder._id || folder.id) === folderId);
	};

	// Helper function to check if an image is selected
	const isImageSelected = (imageId) => {
		return selectedImages.some((image) => image._id === imageId);
	};

	const handleRenameFolder = (folderId) => {
		//Code_Change handle folder rename functionality
		console.log("Rename folder:", folderId);
	};

	const handleDeleteFolder = (folderId, folderName) => {
		// Store folder info for deletion
		setFolderToDelete({ id: folderId, name: folderName });
		// Open confirmation modal
		openModal();
	};

	const confirmDeleteFolder = async () => {
		if (!folderToDelete) return;

		try {
			await deleteFolder(folderToDelete.id).unwrap();

			// Show success toast
			toast.success(`Folder "${folderToDelete.name}" deleted successfully`);

			// Reload folders to reflect the deletion
			await loadFolders();

			// Clear selection if the deleted folder was selected
			const isSelectedFolder = selectedFolders.some(
				(folder) => (folder._id || folder.id) === folderToDelete.id
			);
			if (isSelectedFolder) {
				dispatch(setSelectedFolders([]));
			}

			// Reset folder to delete state
			setFolderToDelete(null);
		} catch (error) {
			console.error("Failed to delete folder:", error);
			toast.error(`Failed to delete folder "${folderToDelete.name}"`);
		}
	};

	const confirmDeleteMedia = async () => {
		if (!mediaToDelete) return;

		try {
			const response = await deleteMedia(mediaToDelete._id).unwrap();
			if (response.status === "success") {
				toast.success(`Media "${mediaToDelete.title}" deleted successfully`);
				// Refresh the media list
				await loadMediaList(true);
			} else {
				toast.error(response.message || "Failed to delete media");
			}
		} catch (error) {
			console.error("Error deleting media:", error);
			toast.error(error?.data?.message || "Failed to delete media");
		} finally {
			handleCloseDeleteModal();
		}
	};

	// Image handlers
	const handleImageSelect = (mediaItem) => {
		dispatch(
			toggleImageSelection({
				mediaId: mediaItem._id,
				mediaItem: mediaItem,
			})
		);
	};

	const handleRenameImage = (mediaItem) => {
		// Check if we have updated data in cache, otherwise use the original media item
		const mediaToUse = updatedMediaCache[mediaItem._id] || mediaItem;
		// Open edit modal with the selected media item (potentially updated)
		setMediaToEdit(mediaToUse);
		setShowEditMedia(true);
	};

	const handleCloseEditModal = () => {
		setShowEditMedia(false);
		setMediaToEdit(null);
	};

	const handleUpdateMedia = async (updatedMediaItem) => {
		// Store the updated media item in cache
		if (updatedMediaItem && updatedMediaItem._id) {
			setUpdatedMediaCache((prev) => ({
				...prev,
				[updatedMediaItem._id]: updatedMediaItem,
			}));
		}

		// Refresh the media list after update
		await loadMediaList(true);
	};

	const handleDeleteImage = (mediaItem) => {
		// Store media info for deletion
		setMediaToDelete(mediaItem);
		// Open confirmation modal
		openModal();
	};

	const handleAddImage = (imageId) => {
		// Find the media item by ID
		const mediaItem = media.find((item) => item._id === imageId);
		if (!mediaItem) {
			console.error("Media item not found:", imageId);
			return;
		}

		// Select the image if not already selected
		if (!selectedImages.some((image) => image._id === imageId)) {
			dispatch(
				toggleImageSelection({
					mediaId: imageId,
					mediaItem: mediaItem,
				})
			);
		}

		// Prepare the selected file data
		const updatedImage = updatedMediaCache[imageId] || mediaItem;
		const selectedFile = {
			id: updatedImage._id,
			url: updatedImage.url,
			imageUrl: updatedImage.url,
			title: updatedImage.title,
			altName: updatedImage.alt || updatedImage.title,
			caption: updatedImage.caption || "",
			courtesy: updatedImage.courtesy || ""
		};

		runMediaLibraryCallback([selectedFile]);
		dispatch(closeMediaLibrary());
		dispatch(resetMedia());
	};

	// Handle Add to Page functionality
	const handleAddToPage = () => {
		if (!selectedImages || selectedImages.length === 0) {
			dispatch(resetMedia());
			dispatch(closeMediaLibrary());
			clearMediaLibraryCallback();
		} else {
			// Prepare selected files with all required details
			const selectedFiles = selectedImages.map((image) => {
				const updatedImage = updatedMediaCache[image._id] || image;
				return {
					id: updatedImage._id,
					url: updatedImage.url,
					imageUrl: updatedImage.url,
					title: updatedImage.title,
					altName: updatedImage.alt || updatedImage.title,
					caption: updatedImage.caption || "",
					courtesy: updatedImage.courtesy || ""
				};
			});
			
			runMediaLibraryCallback(selectedFiles);
			dispatch(closeMediaLibrary());
			dispatch(resetMedia());
		}
	};

	return (
		<>
			<div className={styles.modal}>
				<OutsideClickHandler
					onOutsideClick={() => {
						// Don't close media library if any modal is open
						if (!showCreateFolder && !isModalOpen && !showEditMedia) {
							dispatch(resetMedia());
							dispatch(closeMediaLibrary());
						}
					}}
				>
					<div className={styles.card}>
						<div className={styles.header}>
							<h4 className={styles.title}>Choose Media</h4>
							<FiX
								className={styles.closeBtn}
								onClick={() => {
									dispatch(resetMedia());
									dispatch(closeMediaLibrary());
								}}
							/>
						</div>
						<div className={styles.cardBody}>
							<Aside fileType={fileType} folderPath={folderPath} currentFolder={currentFolder} />
							<div className={styles.mainContainer}>
								<div className="grid grid-cols-12">
									<div
										className="col-span-full md:col-span-9 px-5 flex flex-col border-r"
										style={{ height: "100%" }}
									>
										{/* Fixed Header Section */}
										<div className="flex-shrink-0 z-10 pb-4">
											<Filter />
											<div className="w-full flex justify-between items-center">
												<div
													className="cursor-pointer hover:text-blue-600"
													onClick={() => handleBreadcrumbClick(-1)}
												>
													{getBreadcrumbPath()}
												</div>
												<div>
													<PiFolderSimplePlusThin
														className="text-3xl cursor-pointer hover:text-gray-600 transition-colors"
														onClick={handleAddFolderClick}
														title="Add new folder"
													/>
												</div>
											</div>
										</div>

										{/* Scrollable Content Section */}
										<div 
											ref={scrollContainerRef}
											className="overflow-y-scroll h-[calc(100vh-300px)] scrollbar pt-5 pb-20 px-10 relative">
											{/* Show centered loader when either folders or media are loading */}
											{(isLoadingFolders || isLoadingMedia) && (
												<div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
													<AnimatedButtonLoader />
												</div>
											)}

											{/* Folders Section */}
											{!isLoadingFolders && (
												<div className="flex flex-wrap gap-y-5 gap-x-10">
													{folders.map((folder) => (
														<div
															key={folder._id}
															className={`group relative transition-all duration-200 ${
																isFolderSelected(folder._id)
																	? "ring-[1.5px] ring-offset-4 ring-blue-500"
																	: ""
															}`}
														>
															{/* Folder Icon */}
															<div className="relative">
																<MdFolder
																	onClick={() => handleShowFolderDetails(folder)}
																	onDoubleClick={() => handleFolderEnter(folder)}
																	className={`text-6xl cursor-pointer transition-all duration-100 ease-in-out hover:opacity-70 ${
																		isFolderSelected(folder._id)
																			? "text-blue-500 fill-blue-500"
																			: "text-blue-300 fill-blue-300"
																	}`}
																/>

																{/* Kebab Menu Component */}
																<KebabMenu
																	itemId={folder._id}
																	itemType="folder"
																	onRename={handleRenameFolder}
																	onDelete={() => handleDeleteFolder(folder._id, folder.name)}
																/>
															</div>
															<div className="text-xs mt-1" title={folder.name}>
																{folder.name.length > 12
																	? folder.name.substring(0, 12) + "..."
																	: folder.name}
															</div>
														</div>
													))}
												</div>
											)}

											{/* Media Section */}
											{!isLoadingMedia && (
												<>
													<div className="flex flex-wrap gap-y-5 gap-x-10 mt-10 transition-opacity duration-1000">
														{media.map((mediaItem) => (
															<div
																key={mediaItem._id}
																className={`group relative transition-all duration-200 ${
																	isImageSelected(mediaItem._id)
																		? "ring-[1.5px] ring-offset-4 ring-blue-500"
																		: ""
																}`}
															>
																{/* Image */}
																<div className="relative">
																	<img
																		src={mediaItem.url}
																		alt={mediaItem.alt || mediaItem.title}
																		className={`w-24 h-24 object-cover cursor-pointer transition-all duration-100 ease-in-out hover:opacity-70 ${
																			isImageSelected(mediaItem._id) ? "opacity-90" : ""
																		}`}
																		onClick={() => handleImageSelect(mediaItem)}
																	/>

																	{/* Kebab Menu Component for Images */}
																	<KebabMenu
																		itemId={mediaItem._id}
																		itemType="image"
																		onRename={() => handleRenameImage(mediaItem)}
																		onDelete={() => handleDeleteImage(mediaItem)}
																		onAdd={handleAddImage}
																	/>
																</div>
																<div className="text-xs mt-1" title={mediaItem.title}>
																	{mediaItem.title.length > 12
																		? mediaItem.title.substring(0, 12) + "..."
																		: mediaItem.title}
																</div>
															</div>
														))}
													</div>

													{/* Load More Button */}
													{/* Scroll anchor element */}
													<div id="scroll-anchor"></div>
													
													{mediaPagination.hasMore && media.length > 0 && (
														<div className="text-center mt-8">
															<Button
																size="sm"
																variant="primary"
																rounded="full"
																onClick={(e) => {
																	e.preventDefault();
																	handleLoadMore();
																}}
																disabled={isLoadingMedia}
															>
																{isLoadingMedia ? "Loading..." : "Load More"}
															</Button>
														</div>
													)}
												</>
											)}
										</div>
									</div>
									<div className="col-span-full md:col-span-3 px-5 mt-[30px] pb-10 overflow-y-auto h-[calc(100vh-200px)] scrollbar">
										{/* Show File Info when images are selected */}
										{selectedImages.length > 0 && (
											<FileInfo
												onRefresh={handleRefreshMediaLibrary}
												selectedMedia={selectedImages.map(
													(image) => updatedMediaCache[image._id] || image
												)}
												isMultiple={isMultiple && selectedImages.length > 1}
												onUpdateMedia={handleUpdateMedia}
											/>
										)}

										{/* Show Folder Info when folders are selected */}
										{selectedFolders.length > 0 && selectedImages.length === 0 && (
											<div>
												<div className="relative flex flex-col justify-center group cursor-move mb-6">
													<div className="w-full mx-auto aspect-square overflow-hidden bg-gray-100 relative transition-all duration-100 ease-in-out flex items-center justify-center">
														<MdFolder className="text-8xl text-blue-300" />
													</div>
													<div className="text-sm mt-4 text-center">
														{selectedFolders.length === 1
															? selectedFolders[0].name
															: `${selectedFolders.length} folders selected`}
													</div>
												</div>

												<div className="text-sm space-y-2">
													<div className="flex justify-between">
														<span className="font-medium">Type:</span>
														<span>Folder</span>
													</div>
													<div className="flex justify-between">
														<span className="font-medium">Selected:</span>
														<span>
															{selectedFolders.length} folder{selectedFolders.length > 1 ? "s" : ""}
														</span>
													</div>
												</div>
											</div>
										)}

										{/* Show default content when nothing is selected */}
										{selectedImages.length === 0 && selectedFolders.length === 0 && (
											<>
												<div>
													<img
														src="/svg/placeholder-file.svg"
														alt="Placeholder"
														className="h-32 w-full object-cover mt-[30px] mx-auto"
													/>
													<div className="text-sm mt-10">No file selected</div>
												</div>

												<div className="my-5 h-[1px] bg-gray-200"></div>
												<div>
													<h5 className="font-semibold">Actions</h5>
													<button
														onClick={handleAddFolderClick}
														className="mt-2 bg-transparent text-sm flex items-center gap-x-2 text-primary"
													>
														<PiFolderSimplePlusThin className="text-xl" />
														Create New Folder
													</button>
												</div>

												<div className="my-5 h-[1px] bg-gray-200"></div>
												<div>
													<h5 className="font-semibold">Information</h5>
													<p className="text-sm">
														Organize file types and folders to keep your media library organized.
													</p>
												</div>
											</>
										)}
									</div>
								</div>
							</div>
							{/* Add to Page Button */}
							<div className="w-full flex justify-end absolute  -bottom-3 right-2 py-5 bg-white">
								<Button
									variant="primary"
									size="sm"
									rounded="full"
									onClick={handleAddToPage}
									disabled={!selectedImages || selectedImages.length === 0}
									customClasses="!px-8"
								>
									Add to Page
								</Button>
							</div>
						</div>
					</div>

					<div style={{ zIndex: 99999999 }}>
						<ConfirmationModal
							isOpen={isModalOpen}
							isLoading={isDeletingFolder || isDeletingMedia}
							toggleModal={handleCloseDeleteModal}
							message={
								folderToDelete
									? `Are you sure you want to delete the folder "${folderToDelete.name}"? This action cannot be undone.`
									: mediaToDelete
									? `Are you sure you want to delete the media "${mediaToDelete.title}"? This action cannot be undone.`
									: "Are you sure you want to delete this item?"
							}
							onConfirm={folderToDelete ? confirmDeleteFolder : confirmDeleteMedia}
						/>
					</div>
				</OutsideClickHandler>

				{/* Create Folder Modal */}
				{showCreateFolder && (
					<div
						className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10000]"
						onClick={(e) => {
							// Only close if clicking on the backdrop, not the modal content
							if (e.target === e.currentTarget) {
								handleCancelCreateFolder();
							}
						}}
					>
						<div
							className="bg-white rounded-lg p-6 w-96 max-w-md mx-4"
							onClick={(e) => e.stopPropagation()} // Prevent event bubbling
						>
							<h3 className="text-lg font-semibold mb-4">Create New Folder</h3>
							<div className="mb-4">
								<Input
									label="Folder Name"
									type="text"
									value={newFolderName}
									onDebouncedChange={setNewFolderName}
									onEnter={handleCreateFolder}
									placeholder="Enter folder name"
									required={true}
									customClass="focus:border-primary"
								/>
							</div>
							<div className="text-sm text-gray-600 mb-4">
								{folderHierarchy.length === 0
									? "Creating folder in root directory"
									: `Creating folder in: ${folderHierarchy.map((f) => f.name).join(" > ")}`}
							</div>
							<div className="flex justify-end space-x-3">
								<Button
									variant="secondary"
									size="sm"
									rounded="full"
									onClick={(e) => {
										e.stopPropagation();
										handleCancelCreateFolder();
									}}
									disabled={isCreatingFolder}
								>
									Cancel
								</Button>
								<Button
									variant="primary"
									size="sm"
									rounded="full"
									onClick={(e) => {
										e.stopPropagation();
										handleCreateFolder();
									}}
									disabled={!newFolderName.trim() || isCreatingFolder}
								>
									{isCreatingFolder ? "Creating..." : "Create"}
								</Button>
							</div>
						</div>
					</div>
				)}

				{/* Edit Media Modal */}
				{/* <EditMediaModal
					isOpen={showEditMedia}
					mediaItem={mediaToEdit}
					onClose={handleCloseEditModal}
					onUpdate={handleUpdateMedia}
				/> */}
			</div>
		</>
	);
};

export default MediaLibrary;
