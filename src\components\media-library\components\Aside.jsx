import React, { useRef, useCallback } from "react";
import { toast } from "react-toastify";
import { useDispatch, useSelector } from "react-redux";
import styles from "../MediaLibrary.module.css";
import Button from "../../../parts/Button";
import { FiPlus } from "react-icons/fi";
import {
	useUploadMediaMutation,
	useCreateMediaMutation,
	useGetMediaLibraryListMutation,
} from "../../../store/apis/mediaLibraryApi";
import { setMedia, setMediaPagination } from "../../../store/slices/mediaLibrarySlice";
import { useUploadProgress } from "../../../hooks/useUploadProgress";
import UploadProgress from "./UploadProgress";

const Aside = ({ fileType, folderPath, currentFolder }) => {
	const MAX_FILE_SIZE = 700 * 1024;
	const fileInputRef = useRef(null);
	const dispatch = useDispatch();
	const [uploadMedia] = useUploadMediaMutation();
	const [createMedia] = useCreateMediaMutation();
	const [getMediaLibraryList] = useGetMediaLibraryListMutation();

	// Get current filters from Redux state
	const { filters, mediaPagination } = useSelector((state) => state.mediaLibrary);

	// Upload progress management
	const {
		uploads,
		isVisible: isProgressVisible,
		addUpload,
		updateUploadProgress,
		clearUploads,
		hideProgress,
	} = useUploadProgress();

	const handleUploadClick = () => {
		if (fileInputRef.current) {
			fileInputRef.current.click();
		}
	};
	const handleFileChange = async (e) => {
		const selectedFiles = Array.from(e.target.files || []);
		e.target.value = null;

		if (selectedFiles.length === 0) return;
		const oversizedFiles = selectedFiles.filter((file) => file.size > MAX_FILE_SIZE);

		if (oversizedFiles.length > 0) {
			toast.error("Each file must be less than 700KB.");
			return;
		}

		await handleFileUpload(selectedFiles);
	};

	const handleFileUpload = useCallback(
		async (files) => {
			// Step 1: Initialize progress tracking
			const uploadItems = addUpload(files);

			try {
				// Step 2: Simulate upload progress with smooth transitions
				// Initial progress
				uploadItems.forEach((item) => {
					updateUploadProgress(item.id, 10, "uploading");
				});
				await new Promise((resolve) => setTimeout(resolve, 200));

				// Progress to 30%
				uploadItems.forEach((item) => {
					updateUploadProgress(item.id, 30, "uploading");
				});
				await new Promise((resolve) => setTimeout(resolve, 300));

				// Step 3: Upload files to get URLs
				const formData = new FormData();
				files.forEach((file) => formData.append("files", file));
				formData.append("path", folderPath);
				const uploadResponse = await uploadMedia(formData).unwrap();
				console.log("upload response", uploadResponse);

				if (uploadResponse.status === "success") {
					// Update progress to 60% after successful upload
					uploadItems.forEach((item) => {
						updateUploadProgress(item.id, 60, "uploading");
					});

					// Step 4: Create media entries for each uploaded file
					const uploadedFiles = uploadResponse.data.files;
					console.log("uploaded files", uploadedFiles);

					const mediaCreationPromises = uploadedFiles.map(async (file, index) => {
						const uploadItem = uploadItems[index];
						const originalFile = files[index];

						try {
							const mediaData = {
								url: file.url,
								title: originalFile.name.split(".")[0],
								alt: originalFile.name.split(".")[0],
								keywords: "",
								caption: "",
								courtesy: "",
								folder: currentFolder || null,
								imageTags: [],
							};

							// Update progress to 80% before creating media entry
							updateUploadProgress(uploadItem.id, 80, "uploading");

							await createMedia(mediaData).unwrap();

							// Mark as completed
							updateUploadProgress(uploadItem.id, 100, "completed");

							return { success: true, uploadItem };
						} catch (error) {
							// Mark as error
							updateUploadProgress(
								uploadItem.id,
								100,
								"error",
								error.message || "Failed to create media entry"
							);
							return { success: false, uploadItem, error };
						}
					});

					// Wait for all media entries to be created
					const results = await Promise.all(mediaCreationPromises);
					const successCount = results.filter((r) => r.success).length;
					const errorCount = results.filter((r) => !r.success).length;

					// Show appropriate message
					if (errorCount === 0) {
						toast.success(`Successfully uploaded ${successCount} file(s)`);
					} else if (successCount === 0) {
						toast.error(`Failed to upload ${errorCount} file(s)`);
					} else {
						toast.warning(`Uploaded ${successCount} file(s), ${errorCount} failed`);
					}

					// Step 5: Manually refresh the media list if any uploads succeeded
					if (successCount > 0) {
						try {
							const response = await getMediaLibraryList({
								search: filters.search || "",
								folder: currentFolder || "",
								includeSubfolders: false,
								limit: mediaPagination.limit,
								offset: 0,
							}).unwrap();

							if (response.status === "success") {
								dispatch(setMedia(response.data || []));
								dispatch(
									setMediaPagination({
										offset: mediaPagination.limit,
										hasMore: (response.data || []).length === mediaPagination.limit,
										totalCount: response.totalCounts || 0,
									})
								);
							}
						} catch (refreshError) {
							console.error("Error refreshing media list:", refreshError);
						}
					}
				} else {
					// Mark all uploads as failed
					uploadItems.forEach((item) => {
						updateUploadProgress(item.id, 100, "error", uploadResponse.message || "Upload failed");
					});
					toast.error(uploadResponse.message || "Error uploading files");
				}
			} catch (error) {
				// Mark all uploads as failed
				uploadItems.forEach((item) => {
					updateUploadProgress(
						item.id,
						100,
						"error",
						error?.data?.error || error.message || "Something went wrong"
					);
				});

				const errorMessage = error?.data?.error || error.message || "Something went wrong";
				console.error(errorMessage);
				toast.error(errorMessage);
			}
		},
		[
			uploadMedia,
			createMedia,
			getMediaLibraryList,
			dispatch,
			folderPath,
			currentFolder,
			addUpload,
			updateUploadProgress,
			filters.search,
			mediaPagination.limit,
		]
	);

	// Handle retry for failed uploads
	const handleRetry = useCallback(
		async (uploadId) => {
			const upload = uploads.find((u) => u.id === uploadId);
			if (!upload) return;

			// Reset the upload status
			updateUploadProgress(uploadId, 0, "uploading");

			// Retry the upload for this specific file
			await handleFileUpload([upload.file]);
		},
		[uploads, updateUploadProgress, handleFileUpload]
	);

	// Check if any uploads are in progress
	const isUploading = uploads.some((upload) => upload.status === "uploading");

	return (
		<>
			<div className={styles.asideContainer}>
				<div className={styles.asideContainerWrapper}>
					<div className="my-[30px]">
						<Button
							variant="primary"
							size="sm"
							rounded="full"
							customClasses="btn-primary !w-full "
							onClick={handleUploadClick}
							disabled={isUploading}
						>
							<FiPlus className="mr-1 stroke-[2.8]" /> Upload Media
						</Button>
						{/* Hidden file input */}
						<input
							className="hidden"
							type="file"
							ref={fileInputRef}
							accept={fileType || "image/*"}
							multiple={true}
							onChange={handleFileChange}
						/>
					</div>
				</div>
				<div className={styles.asideFooter}>
					{isUploading && (
						<div className="w-full flex justify-center items-center gap-2">
							<span className="text-base">Uploading</span>
							<div className="flex ml-1.5">
								<div className="animate-spin rounded-full h-5 w-5 border-2 border-gray-300 border-t-blue-500"></div>
							</div>
						</div>
					)}
				</div>
			</div>

			{/* Upload Progress UI */}
			{isProgressVisible && (
				<UploadProgress uploads={uploads} onClose={hideProgress} onRetry={handleRetry} />
			)}
		</>
	);
};

export default Aside;
