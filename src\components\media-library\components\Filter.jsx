import React, { useState, useEffect } from "react";
import { setSearchFilter } from "../../../store/slices/mediaLibrarySlice";
import { SearchInput } from "../../../parts/FormComponents";
import styles from "../MediaLibrary.module.css";
import { useDispatch, useSelector } from "react-redux";

const Filter = () => {
	const dispatch = useDispatch();
	const { filters } = useSelector((state) => state.mediaLibrary);
	const [filter, setFilter] = useState({ search: "" });

	// Sync local state with Redux state when component mounts or filters change
	useEffect(() => {
		setFilter({ search: filters.search || "" });
	}, [filters.search]);

	return (
		<>
			<div className={styles.filterWrapper}>
				<div className={styles.searchBar}>
					<SearchInput
						type="text"
						value={filter?.search ?? ""}
						onChange={(value) => {
							setFilter({ search: value });
							dispatch(setSearchFilter(value));
						}}
						placeholder="Looking for something? Type and press Enter"
						className="border shadow rounded w-full md:w-auto h-8"
						inputClass="!h-[2rem]"
					/>
				</div>
			</div>
		</>
	);
};

export default Filter;
