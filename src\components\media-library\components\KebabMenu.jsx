import React, { useState } from "react";
import { HiDotsVertical } from "react-icons/hi";
import { MdDelete, MdAdd } from "react-icons/md";
import Outside<PERSON><PERSON><PERSON>and<PERSON> from "react-outside-click-handler";

/**
 * KebabMenu Component - Reusable 3-dot menu with hover and smart positioning
 *
 * @param {string|number} itemId - Unique identifier for the item
 * @param {string} itemType - Type of item: "folder" or "image"
 * @param {function} onRename - Callback for rename action
 * @param {function} onDelete - Callback for delete action
 * @param {function} onAdd - Callback for add action (images only)
 * @param {string} className - Additional CSS classes for container
 * @param {string} buttonClassName - Additional CSS classes for button
 * @param {string} menuClassName - Additional CSS classes for menu
 *
 * Usage:
 * <div className="group relative">
 *   <YourContent />
 *   <KebabMenu
 *     itemId={item.id}
 *     itemType="folder"
 *     onRename={(id) => console.log('Rename', id)}
 *     onDelete={(id) => console.log('Delete', id)}
 *   />
 * </div>
 */
const KebabMenu = ({
	itemId,
	itemType = "folder", // "folder" or "image"
	onDelete,
	onAdd, // For images - add to selection/page
	className = "",
	buttonClassName = "",
	menuClassName = "",
}) => {
	const [isOpen, setIsOpen] = useState(false);
	const [menuPosition, setMenuPosition] = useState("left-0");

	const handleMenuToggle = (event) => {
		event.stopPropagation();

		if (isOpen) {
			setIsOpen(false);
			return;
		}

		// Calculate position when opening menu
		const button = event.currentTarget;
		const rect = button.getBoundingClientRect();
		const viewportWidth = window.innerWidth;
		const menuWidth = 160; // w-40 = 10rem = 160px

		// Check if menu would overflow on the right
		const wouldOverflowRight = rect.right + menuWidth > viewportWidth - 20; // 20px margin

		setMenuPosition(wouldOverflowRight ? "right-0" : "left-0");
		setIsOpen(true);
	};

	const handleMenuAction = (action, event) => {
		event.stopPropagation();
		setIsOpen(false);

		switch (action) {
			case "delete":
				onDelete && onDelete(itemId);
				break;
			case "add":
				onAdd && onAdd(itemId);
				break;
			default:
				break;
		}
	};

	const getMenuItems = () => {
		const baseItems = [
			{
				action: "delete",
				label: `Delete`,
				icon: <MdDelete className="text-red-500" />,
				className: "text-red-600 hover:bg-red-50",
			},
		];

		// Add "Add to Page" option for images
		if (itemType === "image" && onAdd) {
			baseItems.unshift({
				action: "add",
				label: "Add to Page",
				icon: <MdAdd className="text-green-500" />,
				className: "text-gray-700 hover:bg-green-50",
			});
		}

		return baseItems;
	};

	return (
		<div
			className={`absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${className}`}
		>
			<OutsideClickHandler onOutsideClick={() => setIsOpen(false)}>
				<div className="relative">
					{/* Kebab Menu Button */}
					<button
						onClick={handleMenuToggle}
						className={`p-1 rounded-full bg-white bg-opacity-90 hover:bg-opacity-100 shadow-sm transition-all duration-150 ${buttonClassName}`}
					>
						<HiDotsVertical className="text-gray-700 text-xs" />
					</button>

					{/* Dropdown Menu with Smart Positioning */}
					{isOpen && (
						<div
							className={`absolute mt-1 w-30 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-[9999] transition-all duration-200 ${menuPosition} ${menuClassName}`}
						>
							{getMenuItems().map((item) => (
								<button
									key={item.action}
									onClick={(e) => handleMenuAction(item.action, e)}
									className={`w-full px-3 py-2 text-left text-xs flex items-center gap-2 ${item.className}`}
								>
									{/* {item.icon} */}
									{item.label}
								</button>
							))}
						</div>
					)}
				</div>
			</OutsideClickHandler>
		</div>
	);
};

export default KebabMenu;
