// DropdownMenu.jsx
import React, { useCallback, useEffect, useState } from "react";

// TextEditorToolbar.jsx
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import {
	MdKeyboardArrowDown,
	MdFormatBold,
	MdFormatItalic,
	MdFormatUnderlined,
	MdFormatAlignLeft,
	MdFormatAlignCenter,
	MdFormatAlignRight,
	MdFormatAlignJustify,
	MdOutlineFormatListBulleted,
} from "react-icons/md";
import {
	toggleDropdown,
	updateFormatting,
	closeAllDropdowns,
} from "../../store/slices/editorSlice";
import { DropdownMenu } from "../../parts/FormComponents";
import { PiPencilSimpleThin } from "react-icons/pi";
import { BsArrowLeft, BsQuote } from "react-icons/bs";
import { IoCodeSlashOutline } from "react-icons/io5";
import { AiOutlineMenuFold, AiOutlineMenuUnfold, AiOutlineOrderedList } from "react-icons/ai";
import { CiLineHeight } from "react-icons/ci";
import { GoLink } from "react-icons/go";
import { GrRedo, GrUndo } from "react-icons/gr";
import Button from "../../parts/Button";
import { useLocation, useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
	useGetStoryQuery,
	usePostStoriesMutation,
	useUpdateStoryMutation,
} from "../../store/apis/storiesApi";
import LinkEditor from "./LinkEditor";
import { apiEndpoints } from "../../utils/constants";
import Loader, { FullScreenLoader } from "../../parts/Loader";
import {
	clearErrors,
	resetRestState,
	resetState,
	setErrors,
	setInitialStoryData,
	setViewLink,
} from "../../store/slices/storiesSlice";
import { toast } from "react-toastify";
import PublishModal from "./PublishModal";
import Tippy from "@tippyjs/react";
import "tippy.js/dist/tippy.css"; // Optional: if not already imported

const fontSizes = [6, 8, 10, 12, 14, 16, 18, 20, 24, 30, 36, 40, 48, 64, 72];

const paragraphFontSizeMapping = {
	Paragraph: 16,
	"Heading 2": 48,
	"Heading 3": 36,
	"Heading 4": 30,
	// "Heading 5": 24,
	// "Heading 6": 18,
};

const paragraphOptions = {
	0: "Paragraph",
	2: "Heading 2",
	3: "Heading 3",
	4: "Heading 4",
	// 5: "Heading 5",
	// 6: "Heading 6",
};
const alignButtons = [
	{
		value: "left",
		Icon: MdFormatAlignLeft,
	},
	{
		value: "center",
		Icon: MdFormatAlignCenter,
	},
	{
		value: "right",
		Icon: MdFormatAlignRight,
	},
	{
		value: "justify",
		Icon: MdFormatAlignJustify,
	},
];
const lineHeights = [1, 1.5, 2, 2.5, 3];
export const areAllKeywordsPresent = ({ title, meta }) => {
	const { primaryKeywords = [], title: metaTitle = "", description: metaDescription = "" } = meta;

	if (!primaryKeywords.length) return true; // No keywords to validate

	const normalize = (text) => text?.toLowerCase() || "";

	const missingKeywords = primaryKeywords.filter((keyword) => {
		const normalizedKeyword = normalize(keyword);
		const normalizedTitle = normalize(title);
		const normalizedMetaTitle = normalize(metaTitle);
		const normalizedMetaDescription = normalize(metaDescription);
		let primaryKeywordsError = {};

		if (!normalizedTitle.includes(normalizedKeyword)) {
			primaryKeywordsError.title = "Title";
		}
		if (!normalizedMetaTitle.includes(normalizedKeyword)) {
			primaryKeywordsError.metaTitle = "Meta Title";
		}
		if (!normalizedMetaDescription.includes(normalizedKeyword)) {
			primaryKeywordsError.metaDescription = "Meta Description";
		}

		if (Object.keys(primaryKeywordsError).length > 0) {
			toast.warning(
				`Missing primary keywords in content: ${Object.values(primaryKeywordsError).join(", ")}`,
				{ toastId: "primary_keyword_warning" }
			);
		}

		return Object.keys(primaryKeywordsError).length > 0;
	});

	return missingKeywords.length === 0;
};
const TextEditorToolbar = ({ setLinkOpen, linkOpen, method, editor }) => {
	const [isPublish, setIsPublish] = useState(false);
	const [arePrimaryKeywordsValid, setArePrimaryKeywordsValid] = useState(true);
	const [missingKeywordFields, setMissingKeywordFields] = useState([]);

	const {
		user: { clientLink: frontendUrl },
	} = useSelector((state) => state.user);
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const { dropdowns, formatting } = useSelector((state) => state.editor, shallowEqual);

	const { pathname } = useLocation();
	const { storyId } = useParams();
	const { storiesState, errors, croppedImg } = useSelector((state) => state.stories);

	const [postStories, { isLoading, isError, error, isSuccess: isPostSuccess }] =
		usePostStoriesMutation();
	const [updateStory, { isLoading: isUpdating, isSuccess: isUpdateSuccess }] =
		useUpdateStoryMutation();
	const {
		data,
		isLoading: isStoryLoading,
		isError: isStoryError,
		error: storyError,
	} = useGetStoryQuery(storyId, { skip: !storyId });

	const handleOptionSelect = (value, type) => {
		dispatch(updateFormatting({ [type]: value }));
		dispatch(closeAllDropdowns());
	};

	function updateStatuses() {
		dispatch(clearErrors());
		dispatch(setErrors(null));
		dispatch(resetRestState());
	}

	// handling the paragraph style change here
	function updateParagraphStyle(value) {
		const newValue = parseInt(value, 10); // Ensure `value` is a number
		// Map the value to the paragraph name using `paragraphOptions`
		const paragraphName = paragraphOptions[newValue];
		// Get the font size using `paragraphFontSizeMapping`
		const fontSize = paragraphFontSizeMapping[paragraphName];
		// Dispatch formatting updates
		dispatch(updateFormatting({ ["paragraph"]: paragraphName }));
		dispatch(updateFormatting({ ["fontSize"]: `${fontSize}` }));
		if (newValue !== 0) {
			editor.chain().focus().toggleHeading({ level: newValue }).setFontSize(fontSize).run();
		} else {
			editor.chain().focus().setParagraph().setFontSize(fontSize).run();
		}
		dispatch(closeAllDropdowns());
	}

	// handling the font size change here
	const updateFontSize = (value) => {
		if (value) {
			editor.chain().focus().setFontSize(`${value}px`).run();
			dispatch(updateFormatting({ ["fontSize"]: value }));
		}
		dispatch(closeAllDropdowns());
	};

	const createTrigger = useCallback(
		(content, dropdownKey) => (
			<button
				className="px-3 py-1 hover:bg-gray-100 rounded-md flex items-center gap-1"
				onClick={() => dispatch(toggleDropdown(dropdownKey))}
			>
				{content}
				<MdKeyboardArrowDown size={16} />
			</button>
		),
		[dispatch]
	);

	const getAlignIcon = () => {
		const AlignIcon =
			{
				left: MdFormatAlignLeft,
				center: MdFormatAlignCenter,
				right: MdFormatAlignRight,
				justify: MdFormatAlignJustify,
			}[formatting.align] || MdFormatAlignLeft;

		return <AlignIcon size={20} />;
	};

	useEffect(() => {
		if (data) {
			dispatch(setInitialStoryData(data.data));
		}
	}, [data]);

	useEffect(() => {
		const {
			primaryKeywords = [],
			title: metaTitle = "",
			description: metaDescription = "",
		} = storiesState.meta;

		if (!primaryKeywords.length) {
			setArePrimaryKeywordsValid(true);
			setMissingKeywordFields([]);
			return;
		}

		const normalize = (text) => text?.toLowerCase() || "";

		let errorsSet = new Set();

		primaryKeywords.forEach((keyword) => {
			const normalizedKeyword = normalize(keyword);

			if (!normalize(storiesState.title).includes(normalizedKeyword)) {
				errorsSet.add("Title");
			}
			if (!normalize(metaTitle).includes(normalizedKeyword)) {
				errorsSet.add("Meta Title");
			}
			if (!normalize(metaDescription).includes(normalizedKeyword)) {
				errorsSet.add("Meta Description");
			}
		});

		const errorArray = Array.from(errorsSet);
		setArePrimaryKeywordsValid(errorArray.length === 0);
		setMissingKeywordFields(errorArray);
	}, [
		storiesState.title,
		storiesState.meta.title,
		storiesState.meta.description,
		storiesState.meta.primaryKeywords,
	]);
	const {
		contributors,
		isHighlighted,
		highligthedSubCategory,
		subcategory,
		section,
		caption,
		title,
		courtesy,
		status: dataStatus,
		tags,
		altName,
		publishDate,
		coverImg,
		writer,
		duplicationNote,
		excerpt,
		articleTitle,
		articleSubheading,
		platform,
		views,
		coverImgFocalPosition,
		week,
		weekendBoxOffice,
		cumulativeBoxOffice,
		template,
		meta: { slug, ...restMeta },
		isPromotional,
		viewLink,
		ogImg,
		twitterImg,
		og,
		twitter,
		content,
		reviews,
	} = storiesState;
	const validateForm = () => {
		const newErrors = {};
		if (!title) {
			newErrors.title = "Article Title";
		}
		if (!subcategory || subcategory === "") {
			newErrors.subcategory = "Subcategory";
		}
		if (!slug) {
			newErrors.slug = "Slug";
		}
		if (!coverImg) {
			newErrors.coverImg = "Cover Image";
		}
		return newErrors;
	};

	const handleUpdateSave = async ({ status, publishDate }) => {
		const validationErrors = validateForm();
		// if (!areAllKeywordsPresent({ title, meta: restMeta })) {
		// 	// Optional: stop publishing process
		// 	return;
		// }
		if (Object.keys(validationErrors).length > 0) {
			const errorMessages = Object.values(validationErrors).join(", ");
			// Display combined errors in Toastify
			toast.error(`${errorMessages} is required`);
			dispatch(setErrors(validationErrors));
		} else {
			dispatch(clearErrors());

			const returnStatus = () => {
				if (method !== "POST") {
					if (parseInt(status, 10) === 3) {
						return data.data.status;
					} else {
						return parseInt(status, 10);
					}
				} else {
					return parseInt(status, 10);
				}
			};
			const payload = {
				title,
				contributor: contributors,
				isHighlighted,
				tag: tags,
				status: returnStatus(),
				highligthedSubCategory,
				subcategory,
				content: JSON.stringify(content),
				section,
				caption,
				courtesy,
				altName,
				publishDate: publishDate || null,
				writer,
				excerpt,
				duplicationNote,
				articleTitle,
				articleSubheading,
				platform,
				views,
				week,
				weekendBoxOffice,
				coverImgFocalPosition,
				cumulativeBoxOffice,
				template,
				meta: {
					...restMeta,
					og: {
						title: og.title,
						description: og.description,
					},
					twitter: {
						title: twitter.title,
						description: twitter.description,
						card: twitter.card === "large" ? "summary_large_image" : "summary",
					},
				},
				isPromotional,
				slug: `/${slug}`,
				reviews,
			};
			const formData = new FormData();

			const isFile = (value) => {
				// Return false for empty values or arrays
				if (!value || (Array.isArray(value) && value.length === 0)) return false;

				// Check if value is a Blob or File
				if (value instanceof Blob || value instanceof File) return false;

				// Check if value is a string (URL or path)
				if (typeof value === "string") return true;

				return false;
			};

			// Apply checks
			if (isFile(coverImg) && coverImg) {
				payload.coverImg = coverImg;
			}

			if (isFile(ogImg) && ogImg) {
				payload.meta.og.image = ogImg;
			}

			if (isFile(twitterImg) && twitterImg) {
				payload.meta.twitter.image = twitterImg;
			}
			if (isFile(croppedImg) && croppedImg) {
				payload.meta.croppedImg = croppedImg;
			}

			if (!isFile(coverImg) && coverImg) {
				formData.append("coverImg", coverImg[0]);
			}
			if (!isFile(ogImg) && ogImg) {
				formData.append("ogImg", ogImg[0]);
			}
			if (!isFile(twitterImg) && twitterImg) {
				formData.append("twitterImg", twitterImg[0]);
			}
			if (
				!isFile(croppedImg) &&
				croppedImg &&
				!(Array.isArray(croppedImg) && croppedImg.length === 0)
			) {
				formData.append("croppedImg", croppedImg[0]);
			}

			formData.append("data", JSON.stringify(payload));

			if (method === "POST") {
				try {
					const response = await postStories({
						data: formData,
						url: "api/article/save",
					}).unwrap();
					if (response.status === "success") {
						dispatch(setViewLink(response?.data?.viewLink));
						toast.success("Story Created successfully!", {
							toastId: "create_success",
						});
						if (parseInt(status, 10) === 1 || parseInt(status, 10) === 4) {
							window.close();
							updateStatuses();
						} else {
							if (response?.data?._id) {
								navigate(`/admin/stories/edit/${response?.data?._id}/?location=stories`);
								updateStatuses();
							}
						}
					}
					// Dispatch items to redux store
				} catch (fetchError) {
					console.error("Failed to fetch items", fetchError);
				}
			} else {
				try {
					const response = await updateStory({
						data: formData,
						url: `${apiEndpoints.patchTextStory}${storyId}`,
					}).unwrap();
					if (response.status === "success") {
						toast.success("Story updated successfully!", { toastId: "update_success" });
						if (parseInt(status, 10) === 1 || parseInt(status, 10) === 4) {
							window.close();
							updateStatuses();
						}
					}
					// Dispatch items to redux store
				} catch (fetchError) {
					console.error("Failed to fetch items", fetchError);
				}
			}
		}
	};

	// handle preveiw url here. It is enabled only for the Edit story
	const handlePreviewUrl = () => {
		if (viewLink) {
			const url = `${frontendUrl}${viewLink ?? ""}`;
			window.open(`${url}`, "_blank");
		}
	};

	const isReviewsSectionFilled = Object.keys(reviews).some((key) => reviews[key]);

	const isAllMandatoryFieldsFilled = [
		"title",
		"cast",
		"director",
		"releasePlatform",
		"publishDate",
	].every((field) => reviews[field]);

	const isSaveButtonDisabled = !(
		storiesState.subcategory &&
		storiesState.title &&
		storiesState.meta.slug &&
		storiesState.meta.title &&
		storiesState.meta.description &&
		(!isReviewsSectionFilled || isAllMandatoryFieldsFilled) &&
		arePrimaryKeywordsValid
	);
	const missingFields = [];

	if (!storiesState.subcategory) missingFields.push("Subcategory");
	if (!storiesState.title) missingFields.push("Story Title");
	if (!storiesState.meta.slug) missingFields.push("Slug");
	if (!storiesState.meta.title) missingFields.push("Meta Title");
	if (!storiesState.meta.description) missingFields.push("Meta Description");

	if (isReviewsSectionFilled && !isAllMandatoryFieldsFilled) {
		["title", "cast", "director", "releasePlatform", "publishDate"].forEach((field) => {
			if (!reviews[field]) {
				const labelMap = {
					title: "Review Title",
					cast: "Cast",
					director: "Director",
					releasePlatform: "Release Platform",
					publishDate: "Publish Date",
				};
				missingFields.push(labelMap[field]);
			}
		});
	}

	if (!arePrimaryKeywordsValid && missingKeywordFields.length > 0) {
		missingFields.push(`Primary Keywords missing in: ${missingKeywordFields.join(", ")}`);
	}
	const tooltipMessage =
		isSaveButtonDisabled && missingFields.length
			? `Please fill the following fields:\n${missingFields.join(", ")}`
			: null;

	if (isLoading || isUpdating || isStoryLoading) {
		return <FullScreenLoader />;
	}

	return (
		<div className="w-full bg-white rounded-md p-2 flex items-center justify-center text-sm px-5">
			<button
				className="px-2 opacity-0 py-1 hover:bg-gray-100 rounded-md flex items-center text-base text-gray-600"
				disabled={true}
			>
				<BsArrowLeft className="text-base mr-2" /> Back
			</button>
			<div className="flex items-center">
				{/* Paragraph Dropdown */}
				<div className="relative">
					<div className=""></div>
					<DropdownMenu
						isOpen={dropdowns.paragraph}
						onSelect={(value) => {
							updateParagraphStyle(value);
						}}
						trigger={createTrigger(formatting.paragraph, "paragraph")}
					>
						{Object.entries(paragraphOptions).map(([value, label]) => (
							<button
								key={value}
								value={`${value}`}
								className="w-full hover:bg-blueShade px-2 py-2 text-xs"
							>
								{label}
							</button>
						))}
					</DropdownMenu>
				</div>
				<div className="h-6 w-px bg-gray-300 mx-2" />
				{/* Font Size Dropdown */}
				<div className="relative">
					<div className="flex flex-col items-cetner w-14 ">
						<DropdownMenu
							isOpen={dropdowns.fontSize}
							onSelect={(value) => updateFontSize(value)}
							trigger={createTrigger(formatting.fontSize, "fontSize")}
						>
							{fontSizes.map((fontSizee, index) => (
								<button
									key={index}
									value={`${fontSizee}`}
									className="w-full hover:bg-blueShade px-2 py-2 text-xs"
								>
									{fontSizee}
								</button>
							))}
						</DropdownMenu>
					</div>
				</div>
				<div className="h-6 w-px bg-gray-300 mx-2" />
				{/* Formatting Buttons */}
				<button
					className={`p-2 rounded-md ${
						editor.isActive("bold") ? "bg-gray-200" : "hover:bg-gray-100"
					}`}
					onClick={() => editor.chain().focus().toggleBold().run()}
					disabled={!editor.can().chain().focus().toggleBold().run()}
				>
					<MdFormatBold size={20} />
				</button>
				<button
					className={`p-2 rounded-md ${
						editor.isActive("italic") ? "bg-gray-200" : "hover:bg-gray-100"
					}`}
					onClick={() => editor.chain().focus().toggleItalic().run()}
					disabled={!editor.can().chain().focus().toggleItalic().run()}
				>
					<MdFormatItalic size={20} />
				</button>
				<button
					className={`p-2 rounded-md ${
						editor.isActive("underline") ? "bg-gray-200" : "hover:bg-gray-100"
					}`}
					onClick={() => editor.chain().focus().toggleUnderline().run()}
					disabled={!editor.can().chain().focus().toggleUnderline().run()}
				>
					<MdFormatUnderlined size={20} />
				</button>

				<div className="flex flex-col items-center hover:bg-gray-100 rounded-md p-2 w-10">
					<label for="hs-color-input" className="block text-sm font-medium">
						A
					</label>
					<input
						type="color"
						className={`h-1.5 w-4 block bg-primary border border-gray-200 cursor-pointer disabled:opacity-50 disabled:pointer-events-none`}
						id="hs-color-input"
						value={`${formatting.textColor}`}
						onChange={(e) => {
							editor.chain().focus().setColor(e.target.value).run();
							handleOptionSelect(e.target.value, "textColor");
						}}
						title="textColor"
					/>
				</div>
				<div className="flex flex-col items-center hover:bg-gray-100 rounded-md p-2 w-10">
					<label for="hs-color-input" className="block text-sm font-medium">
						<PiPencilSimpleThin className="text-base" />
					</label>
					<input
						type="color"
						className="h-1.5 w-4 block bg-primary border border-gray-200 cursor-pointer disabled:opacity-50 disabled:pointer-events-none"
						id="hs-color-input"
						value={formatting.highlightColor}
						onChange={(e) => {
							editor.chain().focus().toggleHighlight({ color: e.target.value }).run();
							handleOptionSelect(e.target.value, "highlightColor");
						}}
						title="highlightColor"
					/>
				</div>

				<div className="h-6 w-px bg-gray-300 mx-2" />
				{/* Quote and Code */}
				<button
					className="p-2 hover:bg-gray-100 rounded-md"
					name="quote"
					type="button"
					onClick={() => {
						editor.chain().focus().toggleBlockquote().run();
					}}
				>
					<BsQuote className="text-base" />
				</button>
				<button
					className="p-2 hover:bg-gray-100 rounded-md"
					type="button"
					name="code"
					onClick={() => {
						editor.chain().focus().toggleCodeBlock().run();
					}}
					// onClick={() => dispatch(toggleFormat("codeBlock"))}
				>
					<IoCodeSlashOutline className="text-base" />
				</button>
				<div className="h-6 w-px bg-gray-300 mx-2" />
				{/* List Buttons */}

				<button
					className="p-2 hover:bg-gray-100 rounded-md"
					type="button"
					name="bulletList"
					onClick={() => editor.chain().focus().toggleOrderedList().run()}
					// onClick={() => dispatch(toggleFormat("orderedList"))}
				>
					<AiOutlineOrderedList className="text-base" />
				</button>
				<button
					className="p-2 hover:bg-gray-100 rounded-md"
					name="bulletList"
					type="button"
					onClick={() => editor.chain().focus().toggleBulletList().run()}
					// onClick={() => dispatch(toggleFormat("bulletList"))}
				>
					<MdOutlineFormatListBulleted className="text-base" />
				</button>
				<div className="h-6 w-px bg-gray-300 mx-2" />
				{/* Alignment Dropdown */}
				<div className="relative ">
					<div className="flex flex-col items-center relative">
						<DropdownMenu
							isOpen={dropdowns.align}
							onSelect={(value) => {
								handleOptionSelect(value, "align");
								if (value) {
									editor.chain().focus().setTextAlign(value).run();
								}
							}}
							trigger={createTrigger(
								<button className="p-2 hover:bg-gray-100 rounded-md">{getAlignIcon()}</button>,
								"align"
							)}
						>
							{alignButtons.map(({ value, Icon }) => (
								<button
									key={value}
									value={value}
									className="w-14 hover:bg-blueShade px-2 py-2 text-xs flex justify-center"
								>
									<Icon className="text-base" />
								</button>
							))}
						</DropdownMenu>
					</div>
				</div>
				<div className="h-6 w-px bg-gray-300 mx-2" />
				<div className="relative ">
					<div className="flex flex-col items-center relative">
						<DropdownMenu
							isOpen={dropdowns.lineHeight}
							onSelect={(value) => {
								handleOptionSelect(value, "lineHeight");
								if (value) {
									editor.chain().focus().setLineHeight(`${value}rem`).run();
								}
							}}
							trigger={createTrigger(
								<button className="p-2 hover:bg-gray-100 rounded-md">
									<CiLineHeight className="text-base" />
								</button>,
								"lineHeight"
							)}
						>
							{lineHeights.map((lineHeight) => (
								<button
									key={lineHeight}
									value={lineHeight}
									className=" hover:bg-blueShade px-2 py-2 text-xs flex justify-center w-14"
								>
									{lineHeight}
								</button>
							))}
						</DropdownMenu>
					</div>
				</div>
				<div className="h-6 w-px bg-gray-300 mx-2" />
				<button
					className="text-base hover:bg-gray-100 px-2 py-2 rounded-md"
					onClick={() => editor.chain().focus().outdent().run()}
				>
					<AiOutlineMenuFold />
				</button>
				<button
					className="text-base hover:bg-gray-100 px-2 py-2 rounded-md"
					onClick={() => editor.chain().focus().indent().run()}
				>
					<AiOutlineMenuUnfold />
				</button>
				<div className="h-6 w-px bg-gray-300 mx-2" />
				<div className="flex items-center gap-x-2  ">
					<div className="relative transition-all duration-200">
						{linkOpen ? (
							<div className="absolute -left-1/2 -translate-x-1/2 z-50 mt-3 bg-white border border-gray-200 shadow-md rounded-md">
								<LinkEditor editor={editor} setShowLink={setLinkOpen} />
							</div>
						) : null}
					</div>
					<button onClick={() => setLinkOpen(!linkOpen)}>
						<GoLink className="text-base" />
					</button>
					{/* History Buttons */}
					<button
						className="p-2 hover:bg-gray-100 rounded-md disabled:text-gray-500"
						onClick={() => editor.chain().focus().undo().run()}
						disabled={!editor.can().undo()}
					>
						<GrUndo className="text-base" />
					</button>
					<button
						className="p-2 hover:bg-gray-100 rounded-md disabled:text-gray-500"
						onClick={() => editor.chain().focus().redo().run()}
						disabled={!editor.can().redo()}
					>
						<GrRedo className="text-base" />
					</button>
					<button
						disabled={isSaveButtonDisabled}
						className="px-4 py-1 relative group text-primary text-base hover:bg-gray-100 disabled:cursor-not-allowed disabled:hover:bg-transparent rounded-md disabled:text-gray-500"
						onClick={() => handleUpdateSave({ status: 3, publishDate: publishDate })}
					>
						{tooltipMessage ? (
							<div className="hidden group-hover:absolute group-hover:block opacity-0 group-hover:opacity-100 transform transition-all duration-300 ease-in-out translate-y-1/2 left-1/2  -translate-x-1/2 bg-gray-600 z-20 text-white px-2 py-1 rounded w-96  whitespace-pre-line">
								{tooltipMessage}
							</div>
						) : null}
						Save
					</button>

					{/* Save and Preview */}

					<button
						onClick={handlePreviewUrl}
						className="px-4 py-1 text-primary text-base disabled:cursor-not-allowed hover:bg-gray-100 disabled:hover:bg-transparent rounded-md disabled:text-gray-500"
						disabled={!viewLink ? true : false}
					>
						Preview
					</button>
					<div className="relative">
						<Button
							customClasses="relative group"
							rounded="full"
							disabled={isSaveButtonDisabled}
							onClick={() => {
								setIsPublish(true);
							}}
						>
							{tooltipMessage ? (
								<div className="hidden group-hover:absolute group-hover:block opacity-0 group-hover:opacity-100 transform transition-all duration-300 ease-in-out translate-y-3/4 left-1/2  -translate-x-1/2 bg-gray-600 z-20 text-white px-2 py-1 rounded w-96  whitespace-pre-line">
									{tooltipMessage}
								</div>
							) : null}
							Publish
						</Button>
						{isPublish ? (
							<PublishModal
								setIsPublish={setIsPublish}
								frontendUrl={frontendUrl}
								handlePreviewUrl={handlePreviewUrl}
								onPublish={handleUpdateSave}
							/>
						) : null}
					</div>
				</div>
			</div>
		</div>
	);
};

export default TextEditorToolbar;
