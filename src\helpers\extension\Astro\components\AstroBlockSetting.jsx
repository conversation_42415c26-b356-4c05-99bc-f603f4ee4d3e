import React, { useEffect, useState, useCallback } from "react";
import { FiX } from "react-icons/fi";
import Button from "../../../../parts/Button";
import { setShow<PERSON>tro } from "../../../../store/slices/storiesSlice";
import { useDispatch } from "react-redux";

const AstroBlockSetting = ({ editor, attrs = {}, open }) => {
  const dispatch = useDispatch();
  const { image = "", caption = "", courtesy = "", alt = "" } = attrs;

  const [data, setData] = useState({
    caption: "",
    courtesy: "",
    alt: "",
  });

  useEffect(() => {
    if (open) {
      setData({ caption, courtesy, alt });
    }
  }, [open, caption, courtesy, alt]);

  const handleOnChange = (e) => {
    const { name, value } = e.target;
    setData((prev) => ({ ...prev, [name]: value }));
  };

  const onClose = useCallback(() => {
    dispatch(setShowAstro(false));
  }, [dispatch]);

  const onSave = useCallback(() => {
    if (!editor) return;

    editor.chain().setAstroCaption(data.caption).run();
    editor.chain().setAstroCourtesy(data.courtesy).run();
    editor.chain().setAstroAlt(data.alt).run();

    onClose();
  }, [editor, data, onClose]);

  return (
    <div
      className={`${
        open ? "block" : "hidden"
      } fixed top-0 right-0 z-[20] h-screen w-full bg-slate-600 bg-opacity-40 rounded-r-md shadow-2xl transition-all duration-800 ease-in-out overflow-hidden`}
    >
      <div className="w-full md:w-96 bg-white absolute right-0 top-0 h-screen text-fadeGray">
        <div className="menu-drawer">
          <div className="headsec">
            <h2 className="heading">Image</h2>
            <FiX className="close cursor-pointer" onClick={onClose} />
          </div>
          <div className="w-full p-5 h-[calc(100vh-129px)] overflow-y-scroll">
            <div className="flex flex-col gap-y-4">
              <div className="flex flex-col gap-y-2">
                <p className="text-base">Image</p>
                <div className="flex justify-center">
                  {image && (
                    <img
                      src={image}
                      alt={alt || "Selected file"}
                      className="max-w-full h-auto rounded-md"
                    />
                  )}
                </div>
              </div>

              <div className="flex flex-col gap-y-2">
                <label htmlFor="caption" className="text-base">
                  Caption
                </label>
                <input
                  id="caption"
                  name="caption"
                  type="text"
                  className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
                  value={data.caption}
                  onChange={handleOnChange}
                  placeholder="Add a caption here"
                />
              </div>

              <div className="flex flex-col gap-y-2">
                <label htmlFor="courtesy" className="text-base">
                  Courtesy
                </label>
                <input
                  id="courtesy"
                  name="courtesy"
                  type="text"
                  className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
                  value={data.courtesy}
                  onChange={handleOnChange}
                  placeholder="Add a courtesy here"
                />
              </div>

              <div className="flex flex-col gap-y-2">
                <label htmlFor="alt" className="text-base">
                  Alt name
                </label>
                <input
                  id="alt"
                  name="alt"
                  type="text"
                  className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
                  value={data.alt}
                  onChange={handleOnChange}
                  placeholder="e.g. A cat sleeping on a white blanket"
                />
              </div>
            </div>
          </div>

          <div className="footersec">
            <Button
              variant="secondary"
              rounded="full"
              className="btn-primary-outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button rounded="full" onClick={onSave}>
              Save
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(AstroBlockSetting);
