import { BubbleMenu as BaseBubbleMenu } from "@tiptap/react";
import React, { useState, useEffect, useCallback, useRef } from "react";
import { getRenderContainer } from "../../../utils/getRenderContainer";
import SizeSmall from "../../../../../public/svg/SizeSmall";
import SizeMedium from "../../../../../public/svg/SizeMedium";
import SizeFullWidth from "../../../../../public/svg/SizeFullWidth";
import SizeOriginal from "../../../../../public/svg/SizeOriginal";
import AlignLeft from "../../../../../public/svg/AlignLeft";
import AlignCenter from "../../../../../public/svg/AlignCenter";
import AlignRight from "../../../../../public/svg/AlignRight";
import "../../../../styles/stories.css";
import { FiRefreshCw, FiSettings, FiX } from "react-icons/fi";
import { DropdownMenu } from "../../../../parts/FormComponents";
import { useDispatch, useSelector } from "react-redux";
import { closeAllDropdowns, toggleDropdown } from "../../../../store/slices/editorSlice";
import { MdKeyboardArrowDown } from "react-icons/md";
import { BiTrash } from "react-icons/bi";
import Button from "../../../../parts/Button";
import { openMediaLibrary, setFolderPath } from "../../../../store/slices/mediaLibrarySlice";
import { folderPath } from "../../../../utils/constants";
import { setMediaLibraryCallback } from "../../../../utils/MediaLibraryManager";

export const imageSizeIcons = {
	50: <SizeSmall />,
	75: <SizeMedium />,
	0: <SizeFullWidth />,
	100: <SizeOriginal />,
};

export const imageAlignIcons = {
	left: <AlignLeft />,
	center: <AlignCenter />,
	right: <AlignRight />,
};

export const ImageBlockMenu = ({ editor, appendTo }) => {
	const menuRef = useRef(null);
	const tippyInstance = useRef(null);
	const { dropdowns } = useSelector((state) => state.editor);
	const [open, setOpen] = useState(false);
	const [caption, setCaption] = useState("");
	const [courtesy, setCourtesy] = useState("");
	const [alt, setAlt] = useState("");
	const [imageContent, setImageContent] = useState({
		size: 100,
		imageAlign: "left",
	});
	const dispatch = useDispatch();

	useEffect(() => {
		if (editor) {
			const attributes = editor.getAttributes("imageBlock");
			setCaption(attributes?.caption ?? "");
			setCourtesy(attributes?.courtesy ?? "");
			setAlt(attributes?.alt ?? "");
		}
	}, [editor, open]);

	const getReferenceClientRect = useCallback(() => {
		const renderContainer = getRenderContainer(editor, "node-imageBlock");
		const rect = renderContainer?.getBoundingClientRect() || new DOMRect(-1000, -1000, 0, 0);

		return rect;
	}, [editor]);

	const shouldShow = useCallback(() => {
		const isActive = editor.isActive("imageBlock");
		return isActive;
	}, [editor]);

	const onImagePropChange = ({ id, value }) => {
		setImageContent({ ...imageContent, [id]: value });
	};

	const onAlignImage = useCallback(
		(alignment) => {
			editor
				.chain()
				.focus(undefined, { scrollIntoView: false })
				.setImageBlockAlign(alignment)
				.run();
		},
		[editor]
	);

	const onWidthChange = useCallback(
		async (value) => {
			setImageContent({ ...imageContent, size: value });
			const currentAttributes = editor.getAttributes("imageBlock");
			const { src } = currentAttributes;

			try {
				// Always get original image dimensions from the actual image
				const img = new Image();
				const { width: originalWidth, height: originalHeight } = await new Promise((resolve, reject) => {
					img.onload = () =>
						resolve({
							width: img.naturalWidth,
							height: img.naturalHeight,
						});
					img.onerror = reject;
					img.src = src;
				});

				// Calculate new pixel dimensions based on size percentage
				let newWidthPx, newHeightPx;

				if (value === 100) {
					// Original size - use natural dimensions
					newWidthPx = originalWidth;
					newHeightPx = originalHeight;
				} else if (value === 0) {
					// Full width - don't set pixel dimensions, let CSS handle it
					newWidthPx = null;
					newHeightPx = null;
				} else {
					// Percentage-based sizing (50% = Small, 75% = Medium)
					const scaleFactor = value / 100;
					newWidthPx = Math.round(originalWidth * scaleFactor);
					newHeightPx = Math.round(originalHeight * scaleFactor);
				}

				// Update width and pixel dimensions
				editor
					.chain()
					.focus(undefined, { scrollIntoView: false })
					.setImageBlockWidth(value)
					.setImageBlockPixelDimensions({ widthPx: newWidthPx, heightPx: newHeightPx })
					.run();
			} catch (error) {
				console.error("Failed to calculate image dimensions:", error);
				// Fallback: just update width without pixel dimensions
				editor.chain().focus(undefined, { scrollIntoView: false }).setImageBlockWidth(value).run();
			}
		},
		[editor]
	);

	const createTrigger = useCallback(
		(content, dropdownKey) => (
			<button
				className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1"
				onClick={() => dispatch(toggleDropdown(dropdownKey))}
			>
				{content}
				<MdKeyboardArrowDown size={16} />
			</button>
		),
		[dispatch]
	);

	const updateImageAttributes = useCallback(() => {
		editor
			.chain()
			.focus(undefined, { scrollIntoView: false })
			.setImageBlockCaption(caption)
			.setImageBlockCourtesy(courtesy)
			.setImageBlockAlt(alt)
			.run();
	}, [editor, caption, courtesy, alt]);

	const handleReplace = () => {
		setMediaLibraryCallback(async (file) => {
			if (file.length === 0) return;

			try {
				// Get new image dimensions
				const img = new Image();
				const { widthPx, heightPx } = await new Promise((resolve, reject) => {
					img.onload = () =>
						resolve({
							widthPx: img.naturalWidth,
							heightPx: img.naturalHeight,
						});
					img.onerror = reject;
					img.src = file[0]?.imageUrl || file[0];
				});

				// Update image with new dimensions
				editor
					.chain()
					.setUpdateImage(file[0]?.imageUrl || file[0])
					.setImageBlockPixelDimensions({ widthPx, heightPx })
					.run();
			} catch (error) {
				console.error("Failed to load new image dimensions:", error);
				// Fallback: just update the image without dimensions
				editor
					.chain()
					.setUpdateImage(file[0]?.imageUrl || file[0])
					.run();
			}
		});
		dispatch(openMediaLibrary());
		dispatch(setFolderPath(folderPath.imageBlock));
	};

	const onRemoveBlock = useCallback(() => {
		editor.chain().focus().deleteSelection().run();
	}, [editor]);

	return (
		<>
			<BaseBubbleMenu
				editor={editor}
				pluginKey="imageBlockMenu"
				shouldShow={shouldShow}
				updateDelay={0}
				tippyOptions={{
					interactive: true,
					offset: [0, 8],
					maxWidth: "100%",
					popperOptions: {
						modifiers: [{ name: "flip", enabled: false }],
					},
					getReferenceClientRect,
					onCreate: (instance) => {
						tippyInstance.current = instance;
					},
					appendTo: () => {
						return appendTo?.current;
					},
				}}
			>
				<div className="bubble-menu" ref={menuRef}>
					<div className="bubble-menu-icon text-fadeGray py-1 text-xs ">
						<DropdownMenu
							isOpen={dropdowns.size}
							onSelect={(value) => {
								onWidthChange(parseInt(value));
								dispatch(closeAllDropdowns());
							}}
							trigger={createTrigger(
								<button className="px-1 rounded-sm flex items-center justify-center">
									{imageSizeIcons[imageContent.size]}
								</button>,
								"size"
							)}
						>
							<button
								value={50}
								className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
							>
								<SizeSmall /> Small
							</button>
							<button
								value={75}
								className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
							>
								<SizeMedium /> Medium
							</button>
							<button
								value={0}
								className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
							>
								<SizeFullWidth /> Full Width
							</button>
							<button
								value={100}
								className="w-full hover:bg-blueShade px-2 whitespace-nowrap py-2 flex items-center gap-2"
							>
								<SizeOriginal /> Original Size
							</button>
						</DropdownMenu>
					</div>
					<span className="bubble-menu-divider"></span>
					<div className="bubble-menu-icon text-fadeGray py-1 text-xs ">
						{/* Dropdown Menu */}
						<DropdownMenu
							isOpen={dropdowns.imageAlignment}
							onSelect={(value) => {
								onAlignImage(value);
								onImagePropChange({ id: "imageAlign", value });
								dispatch(closeAllDropdowns());
							}}
							trigger={createTrigger(
								<button className="px-1 rounded-sm flex items-center justify-center">
									{imageAlignIcons[imageContent.imageAlign]}
								</button>,
								"imageAlignment"
							)}
						>
							<button
								value="left"
								className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
							>
								<AlignLeft /> <span>Align left</span>
							</button>
							<button
								value="center"
								className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
							>
								<AlignCenter /> <span>Align center</span>
							</button>
							<button
								value="right"
								className="w-full hover:bg-blueShade px-2 py-2 whitespace-nowrap flex items-center gap-2"
							>
								<AlignRight /> <span>Align right</span>
							</button>
						</DropdownMenu>
					</div>
					<span className="bubble-menu-divider"></span>
					<button className="bubble-menu-icon text-fadeGray" onClick={() => setOpen(true)}>
						<div className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1">
							<FiSettings />
						</div>
					</button>
					<span className="bubble-menu-divider"></span>
					<button className="bubble-menu-icon text-fadeGray" onClick={handleReplace}>
						<div className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1">
							<FiRefreshCw />
						</div>
					</button>
					<span className="bubble-menu-divider"></span>
					<button className="bubble-menu-icon text-fadeGray" onClick={onRemoveBlock}>
						<div className="p-1 hover:bg-gray-100 rounded-sm flex items-center gap-1">
							<BiTrash />
						</div>
					</button>
				</div>
			</BaseBubbleMenu>
			<div
				className={`${
					open ? "block" : "hidden"
				} fixed top-0 right-0 z-20 h-screen w-full bg-slate-600 bg-opacity-40 rounded-r-md shadow-2xl transition-all duration-800 ease-in-out overflow-hidden`}
			>
				<div className="w-full md:w-96 bg-white absolute right-0 top-0 h-screen text-fadeGray">
					<div className="menu-drawer">
						<div className="headsec">
							<h2 className="heading">Image</h2>
							<FiX className="close" onClick={() => setOpen(false)} />
						</div>
						<div className="w-full p-5 h-[calc(100vh-129px)] overflow-y-scroll">
							<div className="flex flex-col gap-y-4">
								<div className="flex flex-col gap-y-2">
									<p className="text-base">Image</p>
									<div className="flex justify-center">
										<img src={editor.getAttributes("imageBlock")?.src || ""} alt="Selected file" />
									</div>
								</div>
								<div className="flex flex-col gap-y-2">
									<div className="text-base">Caption</div>
									<input
										id="caption"
										type="text"
										className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
										value={caption}
										onChange={(e) => setCaption(e.target.value)}
										placeholder="Add a caption here"
									/>
								</div>
								<div className="flex flex-col gap-y-2">
									<p className="text-base">Courtesy</p>
									<input
										id="courtesy"
										type="text"
										className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
										value={courtesy}
										onChange={(e) => setCourtesy(e.target.value)}
										placeholder="Add a courtesy here"
									/>
								</div>
								<div className="flex flex-col gap-y-2">
									<p className="general-p-small">Alt name</p>
									<input
										id="alt"
										type="text"
										className="border text-sm border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:border-blue-500"
										value={alt}
										onChange={(e) => setAlt(e.target.value)}
										placeholder="e.g. A cat sleeping on a white blanket"
									/>
								</div>
							</div>
						</div>
						<div className="footersec">
							<Button
								variant="secondary"
								rounded="full"
								className="btn-primary-outline"
								onClick={() => setOpen(false)}
							>
								Cancel
							</Button>
							<Button
								rounded="full"
								onClick={() => {
									updateImageAttributes();
									setOpen(false);
								}}
							>
								Save
							</Button>
						</div>
					</div>
				</div>
			</div>
		</>
	);
};

export default ImageBlockMenu;
