import { Editor, NodeViewWrapper } from "@tiptap/react";
import { useCallback } from "react";

import { ImageUploader } from "./ImageUploader";

export const ImageUploadView = ({ getPos, editor }) => {
  const onUpload = useCallback(
    async (url) => {
      if (url) {
        try {
          // Create image element to get dimensions
          const img = new Image();
          
          // Wait for image to load and get dimensions
          const { widthPx, heightPx } = await new Promise((resolve, reject) => {
            img.onload = () => resolve({
              widthPx: img.naturalWidth,
              heightPx: img.naturalHeight
            });
            img.onerror = reject;
            img.src = url;
          });

          // Add image to editor with dimensions
          editor
            .chain()
            .setImageBlock({ src: url, widthPx, heightPx })
            .deleteRange({ from: getPos(), to: getPos() })
            .focus()
            .run();
        } catch (error) {
          console.error('Failed to load image dimensions:', error);
          // Fallback: add image without dimensions
          editor
            .chain()
            .setImageBlock({ src: url })
            .deleteRange({ from: getPos(), to: getPos() })
            .focus()
            .run();
        }
      }
    },
    [getPos, editor]
  );

  return (
    <NodeViewWrapper>
      <div className="p-0 m-0" data-drag-handle>
        <ImageUploader onUpload={onUpload} />
      </div>
    </NodeViewWrapper>
  );
};

export default ImageUploadView;
